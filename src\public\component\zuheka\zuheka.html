<transition :name="transitionDirection" mode="out-in">
  <div>
    <link rel="stylesheet" href="component/zuheka/zuheka.css" />
    <div class="main-right box-border space-x-2 pr-4 pt-2">
      <!--选择订单内容-->
      <div class="o-1vh bg-white flex flex-1 flex-col rounded-lg">
        <div class="o-title-box-shadow shrink-0 p-4">
          <div class="el-input el-input--suffix">
            <input
              type="text"
              autocomplete="off"
              placeholder="请输入服务名称"
              name="search_keyword"
              class="el-input__inner"
              v-model.trim="search_keyword"
              ref="search_keyword"
              @keyup.enter.exact.stop="billingInquiryEnter"
            />
            <span class="el-input__suffix" @click="billingInquiryEnter">
              <span class="el-input__suffix-inner">
                <i class="el-input__icon el-icon-search"></i>
              </span>
            </span>
          </div>
        </div>
        <div
          class="o-scrollbar h-0 flex-1 overflow-y-auto p-4"
          ref="serverCardWrap"
        >
          <div
            class="o-little-server-card-grid-box gap-2"
            v-infinite-scroll="loadMoreProduct"
            infinite-scroll-disabled="isServerScroll"
            infinite-scroll-distance="10"
            infinite-scroll-immediate-check="isServerScroll"
          >
            <div
              class="bg-gray-50 border-gray-200 cursor-pointer rounded-md border border-solid px-4 py-2 transition hover:border-primary"
              v-for="(value,index) in zhk_server_name"
              @click="bind_zhk_add_server(value)"
            >
              <div class="flex h-12 items-center">
                <div class="line-clamp-2 text-lg font-bold leading-5">
                  {{value.service_name}}
                </div>
              </div>
              <div>
                <span class="pr-1 text-xs">￥</span>
                {{value.price}}
              </div>
            </div>
            <!-- 触底效果 -->
            <div v-if="busy" class="loadingtip">{{loadingtip}}</div>
          </div>
        </div>
      </div>
      <!--开单内容-->
      <div
        class="o-1vh bg-white flex shrink-0 flex-col rounded-lg"
        style="width: 650px"
      >
        <member-search-bar :login-info="loginInfo" />
        <div class="o-scrollbar h-0 flex-1 overflow-y-auto">
          <div class="space-y-3 p-4">
            <div
              class="o-service-card bg-white border-gray-200 overflow-hidden rounded-md border border-solid transition hover:shadow-lg"
              v-for="(item,index) in zhk_server_details_name"
            >
              <div
                class="o-card-bg-primary-light border-b-solid border-b-1 border-b-gray-200 px-6 pb-3 pt-6"
              >
                <div class="flex items-center">
                  <div class="pr-4 text-xl font-bold">
                    {{item.service_name}}
                  </div>
                  <div class="o-tag o-tag-indigo text-xs">服务</div>
                  <div class="flex flex-1 items-center justify-end pl-6 pr-4">
                    <div
                      class="el-icon-delete hover:text-red cursor-pointer transition"
                      @click="zhk_open_details_price_del(index)"
                    ></div>
                  </div>
                </div>
                <div class="flex items-center justify-between">
                  <div class="mt-3 flex shrink-0 items-center space-x-6">
                    <div class="o-price-select-tag">
                      <div>原价</div>
                      <div>￥{{item.zhk_price_show}}</div>
                    </div>
                    <div
                      class="o-price-select-tag"
                      v-if="item.member_price != item.zhk_price && item.member_price>0"
                    >
                      <div>会员价</div>
                      <div>￥{{item.member_price | filterMoney}}</div>
                    </div>
                  </div>
                  <div class="flex-1">
                    <!-- 选择提成 -->
                  </div>
                </div>
              </div>
              <div class="flex items-center justify-between px-6 py-4 text-sm">
                <div class="flex items-center">
                  <div class="text-gray-500 shrink-0">单价：</div>
                  <div
                    class="el-input el-input--small el-input--prefix"
                    style="width: 140px"
                  >
                    <input
                      type="text"
                      autocomplete="off"
                      class="el-input__inner"
                      style="
                        font-size: 18px;
                        font-weight: bolder;
                        text-align: right;
                      "
                      v-model.trim="item.unitPrice"
                      @keyup.enter.exact.stop="handleUniPriceChange"
                    />
                    <span class="el-input__prefix center">
                      <span class="el-input__prefix-inner pl-1">￥</span>
                    </span>
                  </div>
                </div>
                <div class="flex items-center">
                  <div class="text-gray-500 shrink-0">数量：</div>

                  <div class="o-numberInput-box" style="width: 130px">
                    <i class="el-icon-minus" @click="jianshao(index)"></i>
                    <input
                      class="el-input__inner text-center"
                      v-model="item.numberAvailable"
                      size="small"
                      style="width: 80px; font-size: 18px; font-weight: bolder"
                      max="9999"
                      step="1"
                      maxlength="4"
                      @change="handleNumInputChange(index)"
                    />
                    <i class="el-icon-plus" @click="zengjia(index)"></i>
                  </div>
                </div>
                <div class="flex items-center">
                  <div class="text-gray-500 shrink-0">小计：</div>
                  <div
                    class="el-input el-input--small el-input--prefix"
                    style="width: 140px"
                  >
                    <input
                      type="text"
                      autocomplete="off"
                      class="el-input__inner"
                      style="
                        font-size: 18px;
                        font-weight: bolder;
                        text-align: right;
                      "
                      v-model.trim="item.subtotal"
                      @keyup.enter.exact.stop="handleUniPriceChange"
                    />
                    <span class="el-input__prefix center">
                      <span class="el-input__prefix-inner pl-1">￥</span>
                    </span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
      <!--选择销售模态框-->
      <el-dialog
        title="选择协助销售"
        :visible.sync="helpStaffVisible"
        width="35%"
        top="7vh"
      >
        <div style="height: calc(100vh - 500px); overflow: auto">
          <el-checkbox-group v-model="checkHelpStaffArr">
            <template v-for="(helpStaff,index) in helpStaffAll">
              <div class="xuazne_xiaoshou" v-if="bindStaffId!=helpStaff.id">
                <el-checkbox
                  :label="helpStaff"
                  style="height: 25px; width: 25px"
                >
                  {{helpStaff.nickname}} ({{helpStaff.job_num}})
                </el-checkbox>
              </div>
            </template>
          </el-checkbox-group>
        </div>
        <span slot="footer" class="dialog-footer">
          <el-button class="cancel-btn" @click="closeHelpStaffVisible(0)">
            取消
          </el-button>
          <el-button type="primary" @click="closeHelpStaffVisible(1)">
            确定
          </el-button>
        </span>
      </el-dialog>

      <!--选择销售模态框-->
      <el-dialog
        title="选择销售"
        :visible.sync="zhk_xiao_shou"
        width="35%"
        top="7vh"
        :append-to-body="true"
      >
        <!--<div class="xuanze_jishi_search">-->
        <!--<el-input placeholder="输入销售名称" suffix-icon="el-icon-search"></el-input>-->
        <!--</div>-->
        <div style="height: calc(100vh - 500px); overflow: auto">
          <div class="xuazne_xiaoshou" v-for="(value , index) in zhkxiaoshous ">
            <el-checkbox
              v-model="value.is_choice_xiaoshou"
              style="height: 25px; width: 25px"
              @change="chioce_xiaoshou(index,value.is_choice_xiaoshou,value.id)"
            >
              {{value.nickname}}
            </el-checkbox>
          </div>
        </div>
        <span slot="footer" class="dialog-footer">
          <el-button class="cancel-btn" @click="zhk_xiaoshou_over">
            取消
          </el-button>
          <el-button type="primary" @click="zhk_xiaoshou_save">确定</el-button>
        </span>
      </el-dialog>
      <!-- 点击充值收款后出现开单框 -->

      <template v-if="buy_receipt">
        <app-pay
          :buy-receipt="buy_receipt"
          :login-info="loginInfo"
          :use-card="isRechargeCard"
          :order-no="orderNo"
          :bill-to-pay="billToPay"
          :is-pay-status="isPayStatus"
          @close-pay="bindClosePay"
        ></app-pay>
      </template>

      <!-- 客户列表框 -->
      <member-search-dialog
        :value="isShowMemberSearch"
        :login-info="loginInfo"
        @sync-is-show-memeber-search="isShowMemberSearch = $event"
        @handle-select-member="handleSelect"
      ></member-search-dialog>
    </div>
  </div>
</transition>
